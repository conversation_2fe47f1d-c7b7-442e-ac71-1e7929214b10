<?php

namespace App\Livewire\User;

use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Barryvdh\DomPDF\Facade\Pdf;

class UserList extends Component
{

    use WithPagination;

    public $search = '';
    public $showModal = false;
    public $isEdit = false; // Property baru untuk menentukan mode

    public $user_id;
    public $name, $email, $password;
    public $showPassword = false;

    protected $rules = [
        'name' => 'required',
        'email' => 'required',
        'password' => 'required',
    ];

    protected $messages = [
        'name.required' => 'Username tidak boleh kosong',
        'email.required' => 'Email tidak boleh kosong',
        'password.required' => 'Password tidak boleh kosong',
    ];

    public function openModal()
    {
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset();
    }

    public function delete($id)
    {
        try {
            $user = User::findOrFail($id);

            if (!$user) {
                $this->dispatch('show-toast', [
                    'icon' => 'error',
                    'message' => 'User tidak ditemukan.',
                ]);
                return;
            }
            $this->user_id = $id;
            $this->dispatch('confirm-delete', [
                'message' => $user->name,
                'icon' => 'warning',
            ]);
        } catch (\Exception $e) {
            $this->dispatch('show-toast', [
                'icon' => 'error',
                'message' => `$e.getMessage()`,
            ]);
        }
    }

    public function deleteConfirm()
    {

        try {
            User::destroy($this->user_id);
            $this->dispatch('show-toast', [
                'icon' => 'success',
                'message' => 'User berhasil dihapus.',
            ]);
        } catch (\Throwable $th) {
            $this->dispatch('show-toast', [
                'icon' => 'error',
                'message' => `$th.getMessage()`,
            ]);
        }
    }

    public function store()
    {

        $this->validate();

        $encrypted = bcrypt($this->password);

        try {
            User::create([
                'name' => $this->name,
                'email' => $this->email,
                'password' => $encrypted,
            ]);

            $this->dispatch('show-toast', [
                'icon' => 'success',
                'message' => 'User berhasil di tambahkan !',
            ]);
        } catch (\Throwable $th) {
            $this->dispatch('show-toast', [
                'icon' => 'error',
                'message' => `$th.getMessage()`,
            ]);
        }
        $this->closeModal();
        $this->reset();
    }

    public function create()
    {
        $this->isEdit = false; // Set mode create
        $this->openModal();
    }

    public function edit($id)
    {
        $this->isEdit = true; // Set mode edit
        $this->showPassword = true;

        $user = User::findOrFail($id);
        $this->user_id = $user->id;
        $this->name = $user->name;
        $this->email = $user->email;
        $this->password = $user->password;



        $this->openModal();
    }




    public function render()
    {
        if ($this->search) {
            $users = User::where('name', 'like', '%' . $this->search . '%')->paginate(10);
        } else {
            $users = User::paginate(10);
        }

        return view('livewire.user.user-list', compact('users'));
    }

    public function exportPdf()
    {
        if ($this->search) {
            $users = User::where('name', 'like', '%' . $this->search . '%')->get();
        } else {
            $users = User::all();
        }

        $pdf = Pdf::loadView('user.pdf.userlist', compact('users'));

        // Opsi 1: Perbaiki streamDownload dengan content yang benar
        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->output(); // Output PDF content
        }, 'data-users-' . date('Y-m-d') . '.pdf');
    }
}
