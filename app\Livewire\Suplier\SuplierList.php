<?php

namespace App\Livewire;

use App\Models\Suplier as ModelsSuplier;
use Livewire\Component;
use Livewire\WithPagination;

class Suplier extends Component
{
    use WithPagination;

    public $search;
    public $paginate;

    public $kode_id;
    public $kode;
    public $nama;
    public $alamat;
    public $no_telp;

    public $showModal = false;
    public $modeEdit = false;

    public function closeModal()
    {
        $this->showModal = false;
    }

    public function openModal()
    {
        $this->showModal = true;
    }

    public function create()
    {
        $this->modeEdit = false;
        $this->showModal = true;
    }

    public function edit($id)
    {

        try {
            $supliers = ModelsSuplier::findOrFail($id);
            $this->kode_id = $supliers->id;
            $this->kode = $supliers->kode;
            $this->nama = $supliers->nama;
            $this->alamat = $supliers->alamat;
            $this->no_telp = $supliers->no_telp;

            $this->modeEdit = true;
            $this->showModal = true;
        } catch (\Throwable $th) {
            throw $th;
        }
    }


    public function render()
    {
        $this->paginate = $this->paginate ? $this->paginate : 10;

        $supliers = ModelsSuplier::where('nama', 'like', '%' . $this->search . '%')
            ->orWhere('kode', 'like', '%' . $this->search . '%')
            ->orWhere('alamat', 'like', '%' . $this->search . '%')
            ->orWhere('no_telp', 'like', '%' . $this->search . '%')
            ->paginate($this->paginate);


        return view('livewire.suplier', compact('supliers'));
    }

    public function delete($id)
    {

        try {
            //code...
            $supliers = ModelsSuplier::find($id);

            if ($supliers) {
                $this->kode_id = $supliers->id;

                $this->dispatch('ConfirmDelete', [
                    'icon' => 'warning',
                    'message' => $supliers->nama
                ]);
            } else {
                $this->kode_id = null;
                $this->dispatch('toast', [
                    'message' => 'Data Tidak Bisa Dihapus, Data Tidak Ditemukan',
                    'icon' => 'error'
                ]);
            }
        } catch (\Throwable $th) {
            $this->dispatch('toast', [
                'message' => $th->getMessage(),
                'icon' => 'error'
            ]);
        }
    }


    public function ConfirmDeleteYes()
    {
        try {
            $supliers = ModelsSuplier::findOrFail($this->kode_id);
            $supliers->delete();

            $this->dispatch('toast', [
                'message' => 'Data Berhasil Dihapus',
                'icon' => 'success'
            ]);
        } catch (\Throwable $th) {
            $this->dispatch('toast', [
                'message' => $th->getMessage(),
                'icon' => 'error'
            ]);
        }
    }
}
